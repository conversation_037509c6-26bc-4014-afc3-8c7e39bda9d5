/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(function webpackUniversalModuleDefinition(root, factory) {
	if(typeof exports === 'object' && typeof module === 'object')
		module.exports = factory(require("react"), require("react-redux"), require("omf-changepackage-components"), require("bwtk"), require("redux"), require("redux-actions"), require("redux-observable"), require("rxjs"), require("react-intl"));
	else if(typeof define === 'function' && define.amd)
		define(["react", "react-redux", "omf-changepackage-components", "bwtk", "redux", "redux-actions", "redux-observable", "rxjs", "react-intl"], factory);
	else {
		var a = typeof exports === 'object' ? factory(require("react"), require("react-redux"), require("omf-changepackage-components"), require("bwtk"), require("redux"), require("redux-actions"), require("redux-observable"), require("rxjs"), require("react-intl")) : factory(root["React"], root["ReactRedux"], root["OMFChangepackageComponents"], root["bwtk"], root["Redux"], root["ReduxActions"], root["ReduxObservable"], root["rxjs"], root["ReactIntl"]);
		for(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];
	}
})(self, function(__WEBPACK_EXTERNAL_MODULE_react__, __WEBPACK_EXTERNAL_MODULE_react_redux__, __WEBPACK_EXTERNAL_MODULE_omf_changepackage_components__, __WEBPACK_EXTERNAL_MODULE_bwtk__, __WEBPACK_EXTERNAL_MODULE_redux__, __WEBPACK_EXTERNAL_MODULE_redux_actions__, __WEBPACK_EXTERNAL_MODULE_redux_observable__, __WEBPACK_EXTERNAL_MODULE_rxjs__, __WEBPACK_EXTERNAL_MODULE_react_intl__) {
return /******/ (function() { // webpackBootstrap
/******/ 	"use strict";
/******/ 	var __webpack_modules__ = ({

/***/ "../src/Widget.tsx":
/*!**************************************!*\
  !*** ../src/Widget.tsx + 20 modules ***!
  \**************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval("{// ESM COMPAT FLAG\n__webpack_require__.r(__webpack_exports__);\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": function() { return /* binding */ Widget; }\n});\n\n// NAMESPACE OBJECT: ../src/store/Actions.ts\nvar Actions_namespaceObject = {};\n__webpack_require__.r(Actions_namespaceObject);\n__webpack_require__.d(Actions_namespaceObject, {\n  getAccountDetails: function() { return getAccountDetails; },\n  getInternetCatalog: function() { return getInternetCatalog; },\n  setAccountDetails: function() { return setAccountDetails; },\n  setInternetCatalog: function() { return setInternetCatalog; },\n  togglePackageSelection: function() { return togglePackageSelection; },\n  updateInternetCatalog: function() { return updateInternetCatalog; }\n});\n\n;// ./tslib/tslib.es6.mjs\n/******************************************************************************\nCopyright (c) Microsoft Corporation.\n\nPermission to use, copy, modify, and/or distribute this software for any\npurpose with or without fee is hereby granted.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\nPERFORMANCE OF THIS SOFTWARE.\n***************************************************************************** */\n/* global Reflect, Promise, SuppressedError, Symbol, Iterator */\n\nvar extendStatics = function(d, b) {\n  extendStatics = Object.setPrototypeOf ||\n      ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n      function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n  return extendStatics(d, b);\n};\n\nfunction __extends(d, b) {\n  if (typeof b !== \"function\" && b !== null)\n      throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n  extendStatics(d, b);\n  function __() { this.constructor = d; }\n  d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n}\n\nvar __assign = function() {\n  __assign = Object.assign || function __assign(t) {\n      for (var s, i = 1, n = arguments.length; i < n; i++) {\n          s = arguments[i];\n          for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n      }\n      return t;\n  }\n  return __assign.apply(this, arguments);\n}\n\nfunction __rest(s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\n      t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\n      for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n          if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\n              t[p[i]] = s[p[i]];\n      }\n  return t;\n}\n\nfunction __decorate(decorators, target, key, desc) {\n  var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n  else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n}\n\nfunction __param(paramIndex, decorator) {\n  return function (target, key) { decorator(target, key, paramIndex); }\n}\n\nfunction __esDecorate(ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {\n  function accept(f) { if (f !== void 0 && typeof f !== \"function\") throw new TypeError(\"Function expected\"); return f; }\n  var kind = contextIn.kind, key = kind === \"getter\" ? \"get\" : kind === \"setter\" ? \"set\" : \"value\";\n  var target = !descriptorIn && ctor ? contextIn[\"static\"] ? ctor : ctor.prototype : null;\n  var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});\n  var _, done = false;\n  for (var i = decorators.length - 1; i >= 0; i--) {\n      var context = {};\n      for (var p in contextIn) context[p] = p === \"access\" ? {} : contextIn[p];\n      for (var p in contextIn.access) context.access[p] = contextIn.access[p];\n      context.addInitializer = function (f) { if (done) throw new TypeError(\"Cannot add initializers after decoration has completed\"); extraInitializers.push(accept(f || null)); };\n      var result = (0, decorators[i])(kind === \"accessor\" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);\n      if (kind === \"accessor\") {\n          if (result === void 0) continue;\n          if (result === null || typeof result !== \"object\") throw new TypeError(\"Object expected\");\n          if (_ = accept(result.get)) descriptor.get = _;\n          if (_ = accept(result.set)) descriptor.set = _;\n          if (_ = accept(result.init)) initializers.unshift(_);\n      }\n      else if (_ = accept(result)) {\n          if (kind === \"field\") initializers.unshift(_);\n          else descriptor[key] = _;\n      }\n  }\n  if (target) Object.defineProperty(target, contextIn.name, descriptor);\n  done = true;\n};\n\nfunction __runInitializers(thisArg, initializers, value) {\n  var useValue = arguments.length > 2;\n  for (var i = 0; i < initializers.length; i++) {\n      value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);\n  }\n  return useValue ? value : void 0;\n};\n\nfunction __propKey(x) {\n  return typeof x === \"symbol\" ? x : \"\".concat(x);\n};\n\nfunction __setFunctionName(f, name, prefix) {\n  if (typeof name === \"symbol\") name = name.description ? \"[\".concat(name.description, \"]\") : \"\";\n  return Object.defineProperty(f, \"name\", { configurable: true, value: prefix ? \"\".concat(prefix, \" \", name) : name });\n};\n\nfunction __metadata(metadataKey, metadataValue) {\n  if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\n}\n\nfunction __awaiter(thisArg, _arguments, P, generator) {\n  function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n  return new (P || (P = Promise))(function (resolve, reject) {\n      function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n      function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n      function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n      step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n}\n\nfunction __generator(thisArg, body) {\n  var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === \"function\" ? Iterator : Object).prototype);\n  return g.next = verb(0), g[\"throw\"] = verb(1), g[\"return\"] = verb(2), typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\n  function verb(n) { return function (v) { return step([n, v]); }; }\n  function step(op) {\n      if (f) throw new TypeError(\"Generator is already executing.\");\n      while (g && (g = 0, op[0] && (_ = 0)), _) try {\n          if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n          if (y = 0, t) op = [op[0] & 2, t.value];\n          switch (op[0]) {\n              case 0: case 1: t = op; break;\n              case 4: _.label++; return { value: op[1], done: false };\n              case 5: _.label++; y = op[1]; op = [0]; continue;\n              case 7: op = _.ops.pop(); _.trys.pop(); continue;\n              default:\n                  if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\n                  if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\n                  if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\n                  if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\n                  if (t[2]) _.ops.pop();\n                  _.trys.pop(); continue;\n          }\n          op = body.call(thisArg, _);\n      } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\n      if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\n  }\n}\n\nvar __createBinding = Object.create ? (function(o, m, k, k2) {\n  if (k2 === undefined) k2 = k;\n  var desc = Object.getOwnPropertyDescriptor(m, k);\n  if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n  }\n  Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n  if (k2 === undefined) k2 = k;\n  o[k2] = m[k];\n});\n\nfunction __exportStar(m, o) {\n  for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(o, p)) __createBinding(o, m, p);\n}\n\nfunction __values(o) {\n  var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n  if (m) return m.call(o);\n  if (o && typeof o.length === \"number\") return {\n      next: function () {\n          if (o && i >= o.length) o = void 0;\n          return { value: o && o[i++], done: !o };\n      }\n  };\n  throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n}\n\nfunction __read(o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o), r, ar = [], e;\n  try {\n      while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n  }\n  catch (error) { e = { error: error }; }\n  finally {\n      try {\n          if (r && !r.done && (m = i[\"return\"])) m.call(i);\n      }\n      finally { if (e) throw e.error; }\n  }\n  return ar;\n}\n\n/** @deprecated */\nfunction __spread() {\n  for (var ar = [], i = 0; i < arguments.length; i++)\n      ar = ar.concat(__read(arguments[i]));\n  return ar;\n}\n\n/** @deprecated */\nfunction __spreadArrays() {\n  for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\n  for (var r = Array(s), k = 0, i = 0; i < il; i++)\n      for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\n          r[k] = a[j];\n  return r;\n}\n\nfunction __spreadArray(to, from, pack) {\n  if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n      if (ar || !(i in from)) {\n          if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n          ar[i] = from[i];\n      }\n  }\n  return to.concat(ar || Array.prototype.slice.call(from));\n}\n\nfunction __await(v) {\n  return this instanceof __await ? (this.v = v, this) : new __await(v);\n}\n\nfunction __asyncGenerator(thisArg, _arguments, generator) {\n  if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n  var g = generator.apply(thisArg, _arguments || []), i, q = [];\n  return i = Object.create((typeof AsyncIterator === \"function\" ? AsyncIterator : Object).prototype), verb(\"next\"), verb(\"throw\"), verb(\"return\", awaitReturn), i[Symbol.asyncIterator] = function () { return this; }, i;\n  function awaitReturn(f) { return function (v) { return Promise.resolve(v).then(f, reject); }; }\n  function verb(n, f) { if (g[n]) { i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; if (f) i[n] = f(i[n]); } }\n  function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\n  function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\n  function fulfill(value) { resume(\"next\", value); }\n  function reject(value) { resume(\"throw\", value); }\n  function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\n}\n\nfunction __asyncDelegator(o) {\n  var i, p;\n  return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\n  function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: false } : f ? f(v) : v; } : f; }\n}\n\nfunction __asyncValues(o) {\n  if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n  var m = o[Symbol.asyncIterator], i;\n  return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\n  function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\n  function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\n}\n\nfunction __makeTemplateObject(cooked, raw) {\n  if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\n  return cooked;\n};\n\nvar __setModuleDefault = Object.create ? (function(o, v) {\n  Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n  o[\"default\"] = v;\n};\n\nvar ownKeys = function(o) {\n  ownKeys = Object.getOwnPropertyNames || function (o) {\n    var ar = [];\n    for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;\n    return ar;\n  };\n  return ownKeys(o);\n};\n\nfunction __importStar(mod) {\n  if (mod && mod.__esModule) return mod;\n  var result = {};\n  if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== \"default\") __createBinding(result, mod, k[i]);\n  __setModuleDefault(result, mod);\n  return result;\n}\n\nfunction __importDefault(mod) {\n  return (mod && mod.__esModule) ? mod : { default: mod };\n}\n\nfunction __classPrivateFieldGet(receiver, state, kind, f) {\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n  return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n}\n\nfunction __classPrivateFieldSet(receiver, state, value, kind, f) {\n  if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n  return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\n}\n\nfunction __classPrivateFieldIn(state, receiver) {\n  if (receiver === null || (typeof receiver !== \"object\" && typeof receiver !== \"function\")) throw new TypeError(\"Cannot use 'in' operator on non-object\");\n  return typeof state === \"function\" ? receiver === state : state.has(receiver);\n}\n\nfunction __addDisposableResource(env, value, async) {\n  if (value !== null && value !== void 0) {\n    if (typeof value !== \"object\" && typeof value !== \"function\") throw new TypeError(\"Object expected.\");\n    var dispose, inner;\n    if (async) {\n      if (!Symbol.asyncDispose) throw new TypeError(\"Symbol.asyncDispose is not defined.\");\n      dispose = value[Symbol.asyncDispose];\n    }\n    if (dispose === void 0) {\n      if (!Symbol.dispose) throw new TypeError(\"Symbol.dispose is not defined.\");\n      dispose = value[Symbol.dispose];\n      if (async) inner = dispose;\n    }\n    if (typeof dispose !== \"function\") throw new TypeError(\"Object not disposable.\");\n    if (inner) dispose = function() { try { inner.call(this); } catch (e) { return Promise.reject(e); } };\n    env.stack.push({ value: value, dispose: dispose, async: async });\n  }\n  else if (async) {\n    env.stack.push({ async: true });\n  }\n  return value;\n}\n\nvar _SuppressedError = typeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\n  var e = new Error(message);\n  return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\n};\n\nfunction __disposeResources(env) {\n  function fail(e) {\n    env.error = env.hasError ? new _SuppressedError(e, env.error, \"An error was suppressed during disposal.\") : e;\n    env.hasError = true;\n  }\n  var r, s = 0;\n  function next() {\n    while (r = env.stack.pop()) {\n      try {\n        if (!r.async && s === 1) return s = 0, env.stack.push(r), Promise.resolve().then(next);\n        if (r.dispose) {\n          var result = r.dispose.call(r.value);\n          if (r.async) return s |= 2, Promise.resolve(result).then(next, function(e) { fail(e); return next(); });\n        }\n        else s |= 1;\n      }\n      catch (e) {\n        fail(e);\n      }\n    }\n    if (s === 1) return env.hasError ? Promise.reject(env.error) : Promise.resolve();\n    if (env.hasError) throw env.error;\n  }\n  return next();\n}\n\nfunction __rewriteRelativeImportExtension(path, preserveJsx) {\n  if (typeof path === \"string\" && /^\\.\\.?\\//.test(path)) {\n      return path.replace(/\\.(tsx)$|((?:\\.d)?)((?:\\.[^./]+?)?)\\.([cm]?)ts$/i, function (m, tsx, d, ext, cm) {\n          return tsx ? preserveJsx ? \".jsx\" : \".js\" : d && (!ext || !cm) ? m : (d + ext + \".\" + cm.toLowerCase() + \"js\");\n      });\n  }\n  return path;\n}\n\n/* harmony default export */ var tslib_es6 = ({\n  __extends,\n  __assign,\n  __rest,\n  __decorate,\n  __param,\n  __esDecorate,\n  __runInitializers,\n  __propKey,\n  __setFunctionName,\n  __metadata,\n  __awaiter,\n  __generator,\n  __createBinding,\n  __exportStar,\n  __values,\n  __read,\n  __spread,\n  __spreadArrays,\n  __spreadArray,\n  __await,\n  __asyncGenerator,\n  __asyncDelegator,\n  __asyncValues,\n  __makeTemplateObject,\n  __importStar,\n  __importDefault,\n  __classPrivateFieldGet,\n  __classPrivateFieldSet,\n  __classPrivateFieldIn,\n  __addDisposableResource,\n  __disposeResources,\n  __rewriteRelativeImportExtension,\n});\n\n// EXTERNAL MODULE: external {\"root\":\"React\",\"commonjs2\":\"react\",\"commonjs\":\"react\",\"amd\":\"react\"}\nvar external_root_React_commonjs2_react_commonjs_react_amd_react_ = __webpack_require__(\"react\");\n// EXTERNAL MODULE: external {\"root\":\"ReactRedux\",\"commonjs2\":\"react-redux\",\"commonjs\":\"react-redux\",\"amd\":\"react-redux\"}\nvar external_root_ReactRedux_commonjs2_react_redux_commonjs_react_redux_amd_react_redux_ = __webpack_require__(\"react-redux\");\n// EXTERNAL MODULE: external {\"root\":\"OMFChangepackageComponents\",\"commonjs2\":\"omf-changepackage-components\",\"commonjs\":\"omf-changepackage-components\",\"amd\":\"omf-changepackage-components\"}\nvar external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_ = __webpack_require__(\"omf-changepackage-components\");\n// EXTERNAL MODULE: external {\"root\":\"bwtk\",\"commonjs2\":\"bwtk\",\"commonjs\":\"bwtk\",\"amd\":\"bwtk\"}\nvar external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_ = __webpack_require__(\"bwtk\");\n// EXTERNAL MODULE: external {\"root\":\"Redux\",\"commonjs2\":\"redux\",\"commonjs\":\"redux\",\"amd\":\"redux\"}\nvar external_root_Redux_commonjs2_redux_commonjs_redux_amd_redux_ = __webpack_require__(\"redux\");\n// EXTERNAL MODULE: external {\"root\":\"ReduxActions\",\"commonjs2\":\"redux-actions\",\"commonjs\":\"redux-actions\",\"amd\":\"redux-actions\"}\nvar external_root_ReduxActions_commonjs2_redux_actions_commonjs_redux_actions_amd_redux_actions_ = __webpack_require__(\"redux-actions\");\n// EXTERNAL MODULE: external {\"root\":\"ReduxObservable\",\"commonjs2\":\"redux-observable\",\"commonjs\":\"redux-observable\",\"amd\":\"redux-observable\"}\nvar external_root_ReduxObservable_commonjs2_redux_observable_commonjs_redux_observable_amd_redux_observable_ = __webpack_require__(\"redux-observable\");\n;// ../src/mutators/index.ts\n\n\nfunction serviceAccountMutatorFn(response) {\n    return (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(response, \"ProductOfferings\", [{ Unavailable: true }]);\n}\nfunction catalogMutatorFn(response) {\n    var productOfferingGroup = (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(response, \"productOfferingDetail.productOfferingGroups\", [])\n        .find(function (group) { return group.lineOfBusiness === external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Volt.ELineOfBusiness.Internet &&\n        group.productOfferingGroupType === external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Volt.EProductOfferingGroupType.Default; });\n    return (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(productOfferingGroup, \"productOfferings\", []);\n}\nfunction orderMutatorFn(response, catalog) {\n    var productOfferingGroup = (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(response, \"productOfferingDetail.productOfferingGroups\", [])\n        .find(function (group) { return group.lineOfBusiness === external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Volt.ELineOfBusiness.Internet &&\n        group.productOfferingGroupType === external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Volt.EProductOfferingGroupType.Delta; });\n    var productOfferings = (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(productOfferingGroup, \"productOfferings\", []);\n    productOfferings.forEach(function (product) {\n        var initial = catalog.find(function (pkg) { return pkg.id === product.id; }) || {};\n        Object.assign(initial, product);\n    });\n    return __spreadArray([], __read(catalog), false);\n}\n\n;// ../src/store/Actions.ts\n\n\nvar getAccountDetails = (0,external_root_ReduxActions_commonjs2_redux_actions_commonjs_redux_actions_amd_redux_actions_.createAction)(\"GET_ACCOUNT_DETAILS\");\nvar setAccountDetails = (0,external_root_ReduxActions_commonjs2_redux_actions_commonjs_redux_actions_amd_redux_actions_.createAction)(\"SET_ACCOUNT_DETAILS\", serviceAccountMutatorFn);\nvar getInternetCatalog = (0,external_root_ReduxActions_commonjs2_redux_actions_commonjs_redux_actions_amd_redux_actions_.createAction)(\"GET_INTERNET_CATALOG\");\nvar setInternetCatalog = (0,external_root_ReduxActions_commonjs2_redux_actions_commonjs_redux_actions_amd_redux_actions_.createAction)(\"SET_INTERNET_CATALOG\", catalogMutatorFn);\nvar togglePackageSelection = (0,external_root_ReduxActions_commonjs2_redux_actions_commonjs_redux_actions_amd_redux_actions_.createAction)(\"TOGGLE_INTERNET_PACKAGE\");\nvar updateInternetCatalog = (0,external_root_ReduxActions_commonjs2_redux_actions_commonjs_redux_actions_amd_redux_actions_.createAction)(\"UPDATE_INTERNET_CATALOG\", orderMutatorFn);\n\n// EXTERNAL MODULE: external {\"root\":\"rxjs\",\"commonjs2\":\"rxjs\",\"commonjs\":\"rxjs\",\"amd\":\"rxjs\"}\nvar external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_ = __webpack_require__(\"rxjs\");\n;// ../src/Config.ts\n\n\nvar BaseConfig = external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.CommonFeatures.BaseConfig, configProperty = external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.CommonFeatures.configProperty;\nvar Config = (function (_super) {\n    __extends(Config, _super);\n    function Config() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    __decorate([\n        configProperty(\"\"),\n        __metadata(\"design:type\", String)\n    ], Config.prototype, \"flowType\", void 0);\n    __decorate([\n        configProperty({}),\n        __metadata(\"design:type\", Object)\n    ], Config.prototype, \"environmentVariables\", void 0);\n    __decorate([\n        configProperty({}),\n        __metadata(\"design:type\", Object)\n    ], Config.prototype, \"mockdata\", void 0);\n    __decorate([\n        configProperty({}),\n        __metadata(\"design:type\", Object)\n    ], Config.prototype, \"headers\", void 0);\n    __decorate([\n        configProperty({ base: \"http://127.0.0.1:8881\" }),\n        __metadata(\"design:type\", Object)\n    ], Config.prototype, \"api\", void 0);\n    Config = __decorate([\n        external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.Injectable\n    ], Config);\n    return Config;\n}(BaseConfig));\n\n\n;// ../src/Client.ts\n\n\n\n\nvar Client = (function (_super) {\n    __extends(Client, _super);\n    function Client(ajaxClient, config) {\n        return _super.call(this, ajaxClient, config) || this;\n    }\n    Client = __decorate([\n        external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.Injectable,\n        __metadata(\"design:paramtypes\", [external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.AjaxServices, Config])\n    ], Client);\n    return Client;\n}(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.BaseClient));\n\n\n;// ../src/store/Epics/Catalog.ts\n\n\n\n\n\n\n\n\nvar errorOccured = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.errorOccured, setWidgetStatus = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.setWidgetStatus, clearCachedState = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.clearCachedState, finalizeRestriction = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.finalizeRestriction;\nvar CatalogEpics = (function () {\n    function CatalogEpics(client, config) {\n        this.client = client;\n        this.config = config;\n        this.widgetState = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetStatus.INIT;\n    }\n    CatalogEpics.prototype.combineEpics = function () {\n        return (0,external_root_ReduxObservable_commonjs2_redux_observable_commonjs_redux_observable_amd_redux_observable_.combineEpics)(this.requestCatalogEpic, this.togglePlanSelectionEpic, this.finalizeRestrictionEpic);\n    };\n    Object.defineProperty(CatalogEpics.prototype, \"requestCatalogEpic\", {\n        get: function () {\n            var _this = this;\n            return function (action$) {\n                return action$.pipe((0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.filter)(function (action) { return action.type === getInternetCatalog.toString(); }), (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.filter)(function () { return _this.widgetState !== external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetStatus.UPDATING; }), (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.mergeMap)(function () {\n                    var _a;\n                    return (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.concat)((0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.of)(setWidgetStatus(_this.widgetState = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetStatus.UPDATING)), _this.client.get(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Utils.appendRefreshOnce(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Utils.getURLByFlowType((_a = {},\n                        _a[external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EFlowType.TV] = _this.config.api.catalogAPI,\n                        _a[external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EFlowType.INTERNET] = _this.config.api.catalogAPI,\n                        _a[external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EFlowType.BUNDLE] = _this.config.api.bundleCatalogAPI,\n                        _a)))).pipe((0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.mergeMap)(function (response) {\n                        return (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.FilterRestrictionObservable)(response, [\n                            setInternetCatalog(response.data),\n                            external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.omniPageLoaded(),\n                            setWidgetStatus(_this.widgetState = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetStatus.RENDERED)\n                        ]);\n                    })));\n                }), (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.catchError)(function (error) { return (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.of)(errorOccured(new external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Models.ErrorHandler(\"getInternetCatalog\", error))); }));\n            };\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(CatalogEpics.prototype, \"togglePlanSelectionEpic\", {\n        get: function () {\n            var _this = this;\n            return function (action$, state$) {\n                return action$.pipe((0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.filter)(function (action) {\n                    return action.type === togglePackageSelection.toString();\n                }), (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.filter)(function () { return _this.widgetState !== external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetStatus.UPDATING; }), (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.mergeMap)(function (_a) {\n                    var payload = _a.payload;\n                    return (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.concat)((0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.of)(setWidgetStatus(_this.widgetState = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetStatus.UPDATING)), _this.client.action(payload).pipe((0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.mergeMap)(function (response) {\n                        return (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.FilterRestrictionObservable)(response, [\n                            updateInternetCatalog(response.data, state$.value.catalog),\n                            clearCachedState([external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetName.PREVIEW]),\n                            setWidgetStatus(_this.widgetState = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetStatus.RENDERED)\n                        ]);\n                    })));\n                }), (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.catchError)(function (error) { return (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.of)(errorOccured(new external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Models.ErrorHandler(\"togglePackageSelection\", error))); }));\n            };\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(CatalogEpics.prototype, \"finalizeRestrictionEpic\", {\n        get: function () {\n            var _this = this;\n            return function (action$, state$) {\n                return action$.pipe((0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.filter)(function (action) {\n                    return action.type === finalizeRestriction.toString();\n                }), (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.filter)(function (_a) {\n                    var payload = _a.payload;\n                    return Boolean(payload) &&\n                        Boolean(payload.productOfferingDetail) &&\n                        _this.widgetState !== external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetStatus.UPDATING;\n                }), (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.mergeMap)(function (_a) {\n                    var payload = _a.payload;\n                    return (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.of)(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.broadcastUpdate(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.setProductConfigurationTotal((0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(payload, \"productOfferingDetail.productConfigurationTotal\"))), updateInternetCatalog(payload, state$.value.catalog), clearCachedState([external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetName.PREVIEW]), setWidgetStatus(_this.widgetState = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetStatus.RENDERED));\n                }));\n            };\n        },\n        enumerable: false,\n        configurable: true\n    });\n    CatalogEpics = __decorate([\n        external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.Injectable,\n        __metadata(\"design:paramtypes\", [Client, Config])\n    ], CatalogEpics);\n    return CatalogEpics;\n}());\n\n\n;// ../src/store/Epics/UserAccount.ts\n\n\n\n\n\n\n\n\nvar UserAccountEpics = (function () {\n    function UserAccountEpics(client, config) {\n        this.client = client;\n        this.config = config;\n        this.widgetState = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetStatus.INIT;\n    }\n    UserAccountEpics.prototype.combineEpics = function () {\n        return (0,external_root_ReduxObservable_commonjs2_redux_observable_commonjs_redux_observable_amd_redux_observable_.combineEpics)(this.requestDataEpic);\n    };\n    Object.defineProperty(UserAccountEpics.prototype, \"requestDataEpic\", {\n        get: function () {\n            var _this = this;\n            return function (action$, state$) {\n                return action$.pipe((0,external_root_ReduxObservable_commonjs2_redux_observable_commonjs_redux_observable_amd_redux_observable_.ofType)(getAccountDetails.toString()), (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.filter)(function () { return _this.widgetState !== external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetStatus.UPDATING; }), (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.mergeMap)(function () { return _this.client.get(_this.config.api.serviceAccountAPI).pipe((0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.mergeMap)(function (_a) {\n                    var data = _a.data;\n                    return (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.of)(setAccountDetails(data), getInternetCatalog());\n                }), (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.catchError)(function (error) { return (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.of)(setAccountDetails({})); })); }));\n            };\n        },\n        enumerable: false,\n        configurable: true\n    });\n    UserAccountEpics = __decorate([\n        external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.Injectable,\n        __metadata(\"design:paramtypes\", [Client, Config])\n    ], UserAccountEpics);\n    return UserAccountEpics;\n}());\n\n\n;// ../src/store/Epics/Omniture.ts\n\n\n\n\n\nvar omniPageLoaded = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.omniPageLoaded, omniPageSubmit = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.omniPageSubmit;\nvar OmnitureEpics = (function () {\n    function OmnitureEpics() {\n        this.widgetState = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetStatus.INIT;\n    }\n    OmnitureEpics.prototype.combineEpics = function () {\n        return (0,external_root_ReduxObservable_commonjs2_redux_observable_commonjs_redux_observable_amd_redux_observable_.combineEpics)(this.pageLoadedEpic, this.pageSubmitEpic);\n    };\n    Object.defineProperty(OmnitureEpics.prototype, \"pageLoadedEpic\", {\n        get: function () {\n            return function (action$, state$) {\n                return action$.pipe((0,external_root_ReduxObservable_commonjs2_redux_observable_commonjs_redux_observable_amd_redux_observable_.ofType)(omniPageLoaded.toString()), (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.mergeMap)(function () {\n                    var accountDetails = state$.value.accountDetails;\n                    var omniture = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Omniture.useOmniture();\n                    omniture.trackFragment({\n                        id: \"InternetPage\",\n                        s_oSS1: \"~\",\n                        s_oSS2: \"~\",\n                        s_oSS3: \"~\",\n                        s_oPGN: \"~\",\n                        s_oAPT: {\n                            actionresult: 1\n                        },\n                        s_oPLE: {\n                            type: external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Omniture.EMessageType.Information,\n                            content: (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(accountDetails, \"0.Name\", \"\")\n                        }\n                    });\n                    return (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.of)();\n                }), (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.catchError)(function (error) { return (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.of)(); }));\n            };\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(OmnitureEpics.prototype, \"pageSubmitEpic\", {\n        get: function () {\n            return function (action$, state$) {\n                return action$.pipe((0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.filter)(function (action) { return action.type === omniPageSubmit.toString(); }), (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.mergeMap)(function () {\n                    var catalog = state$.value.catalog;\n                    var omniture = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Omniture.useOmniture();\n                    omniture.trackAction({\n                        id: \"internetPageSubmit\",\n                        s_oAPT: {\n                            actionId: 647\n                        },\n                        s_oBTN: \"Continue\",\n                        s_oPRD: catalog\n                            .filter(function (pkg) { return (pkg.isSelected && !pkg.isCurrent); })\n                            .map(function (pkg) { return ({\n                            category: \"Internet\",\n                            name: pkg.name,\n                            sku: \"\",\n                            quantity: \"1\",\n                            price: (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(pkg, \"regularPrice.price\", \"0\"),\n                            promo: (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(pkg, \"promotionDetails.promotionalPrice.price\", \"\")\n                        }); })\n                    });\n                    return (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.of)();\n                }), (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.catchError)(function (error) { return (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.of)(); }));\n            };\n        },\n        enumerable: false,\n        configurable: true\n    });\n    OmnitureEpics = __decorate([\n        external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.Injectable\n    ], OmnitureEpics);\n    return OmnitureEpics;\n}());\n\n\n;// ../src/store/Epics.ts\n\n\n\n\n\n\n\n\n\nvar Epics_setWidgetStatus = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.setWidgetStatus;\nvar Epics = (function () {\n    function Epics(catalogEpics, userAccountEpics, omniture) {\n        this.catalogEpics = catalogEpics;\n        this.userAccountEpics = userAccountEpics;\n        this.omniture = omniture;\n    }\n    Epics.prototype.combineEpics = function () {\n        return (0,external_root_ReduxObservable_commonjs2_redux_observable_commonjs_redux_observable_amd_redux_observable_.combineEpics)(this.onWidgetStatusEpic);\n    };\n    Object.defineProperty(Epics.prototype, \"onWidgetStatusEpic\", {\n        get: function () {\n            return function (action$) {\n                return action$.pipe((0,external_root_ReduxObservable_commonjs2_redux_observable_commonjs_redux_observable_amd_redux_observable_.ofType)(Epics_setWidgetStatus.toString()), (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.filter)(function (action) { return action.payload === external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetStatus.INIT; }), (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.mergeMap)(function () {\n                    var action, s_oSS2 = \"~\";\n                    switch (external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Utils.getFlowType()) {\n                        case external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EFlowType.INTERNET:\n                            action = 523;\n                            s_oSS2 = \"Internet\";\n                            break;\n                        case external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EFlowType.BUNDLE:\n                            s_oSS2 = \"Bundle\";\n                            break;\n                    }\n                    external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Omniture.useOmniture().updateContext({\n                        s_oSS1: \"~\",\n                        s_oSS2: s_oSS2,\n                        s_oSS3: \"Change package\",\n                        s_oPGN: \"Setup your service\",\n                        s_oAPT: {\n                            actionId: action\n                        }\n                    });\n                    return [\n                        getAccountDetails()\n                    ];\n                }));\n            };\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Epics = __decorate([\n        external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.Injectable,\n        __metadata(\"design:paramtypes\", [CatalogEpics,\n            UserAccountEpics,\n            OmnitureEpics])\n    ], Epics);\n    return Epics;\n}());\n\n\n;// ../src/Localization.ts\n\n\n\nvar BaseLocalization = external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.CommonFeatures.BaseLocalization;\nvar Localization = (function (_super) {\n    __extends(Localization, _super);\n    function Localization() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    Localization_1 = Localization;\n    Localization.getLocalizedString = function (id) {\n        Localization_1.Instance = Localization_1.Instance || external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.ServiceLocator.instance.getService(external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.CommonServices.Localization);\n        var instance = Localization_1.Instance;\n        return instance ? instance.getLocalizedString(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetName.INTERNET, id, instance.locale) : id;\n    };\n    var Localization_1;\n    Localization.Instance = null;\n    Localization = Localization_1 = __decorate([\n        external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.Injectable\n    ], Localization);\n    return Localization;\n}(BaseLocalization));\n\n\n;// ../src/store/Store.ts\n\n\n\n\n\n\n\n\n\n\nvar BaseStore = external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.CommonFeatures.BaseStore, actionsToComputedPropertyName = external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.CommonFeatures.actionsToComputedPropertyName;\nvar _a = actionsToComputedPropertyName(Actions_namespaceObject), Store_setAccountDetails = _a.setAccountDetails, Store_setInternetCatalog = _a.setInternetCatalog, Store_updateInternetCatalog = _a.updateInternetCatalog;\nvar Store = (function (_super) {\n    __extends(Store, _super);\n    function Store(client, store, epics, localization) {\n        var _this = _super.call(this, store) || this;\n        _this.client = client;\n        _this.epics = epics;\n        _this.localization = localization;\n        return _this;\n    }\n    Object.defineProperty(Store.prototype, \"reducer\", {\n        get: function () {\n            var _a, _b;\n            return (0,external_root_Redux_commonjs2_redux_commonjs_redux_amd_redux_.combineReducers)(__assign(__assign(__assign(__assign({}, external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Reducers.WidgetBaseLifecycle(this.localization)), external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Reducers.WidgetLightboxes()), external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Reducers.WidgetRestrictions()), { accountDetails: (0,external_root_ReduxActions_commonjs2_redux_actions_commonjs_redux_actions_amd_redux_actions_.handleActions)((_a = {},\n                    _a[Store_setAccountDetails] = function (state, _a) {\n                        var payload = _a.payload;\n                        return payload || state;\n                    },\n                    _a), [{}]), catalog: (0,external_root_ReduxActions_commonjs2_redux_actions_commonjs_redux_actions_amd_redux_actions_.handleActions)((_b = {},\n                    _b[Store_setInternetCatalog] = function (state, _a) {\n                        var payload = _a.payload;\n                        return payload || state;\n                    },\n                    _b[Store_updateInternetCatalog] = function (state, _a) {\n                        var payload = _a.payload;\n                        return payload || state;\n                    },\n                    _b), []) }));\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(Store.prototype, \"middlewares\", {\n        get: function () {\n            return (0,external_root_ReduxObservable_commonjs2_redux_observable_commonjs_redux_observable_amd_redux_observable_.combineEpics)(this.epics.omniture.combineEpics(), this.epics.userAccountEpics.combineEpics(), this.epics.catalogEpics.combineEpics(), this.epics.combineEpics(), new external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ModalEpics().combineEpics(), new external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.RestricitonsEpics(this.client, \"INTERNET_RESTRICTION_MODAL\").combineEpics(), new external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.LifecycleEpics().combineEpics());\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Store = __decorate([\n        external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.Injectable,\n        __metadata(\"design:paramtypes\", [Client, external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.Store, Epics, Localization])\n    ], Store);\n    return Store;\n}(BaseStore));\n\n\n;// ../src/store/index.ts\n\n\n\n;// ../src/Pipe.ts\n\n\n\nvar BasePipe = external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.CommonFeatures.BasePipe;\nvar Pipe = (function (_super) {\n    __extends(Pipe, _super);\n    function Pipe(arg) {\n        var _this = _super.call(this, arg) || this;\n        Pipe.instance = _this;\n        return _this;\n    }\n    Pipe.Subscriptions = function (store) {\n        var _a;\n        return _a = {},\n            _a[external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.onContinue.toString()] = function () {\n                store.dispatch(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.omniPageSubmit());\n                external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.broadcastUpdate(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.historyForward());\n            },\n            _a[external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.omniPageSubmit.toString()] = function () {\n                external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.broadcastUpdate(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.omniPageSubmit());\n            },\n            _a;\n    };\n    return Pipe;\n}(BasePipe));\n\n\n// EXTERNAL MODULE: external {\"root\":\"ReactIntl\",\"commonjs2\":\"react-intl\",\"commonjs\":\"react-intl\",\"amd\":\"react-intl\"}\nvar external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_ = __webpack_require__(\"react-intl\");\n;// ../src/views/header/index.tsx\n\n\n\n\n\nvar Component = function (_a) {\n    var accountDetails = _a.accountDetails;\n    var _b = __read(external_root_React_commonjs2_react_commonjs_react_amd_react_.useState(false), 2), expanded = _b[0], toggleState = _b[1];\n    external_root_React_commonjs2_react_commonjs_react_amd_react_.useEffect(function () {\n        if (expanded) {\n            external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Omniture.useOmniture().trackAction({\n                id: \"myCurrentPackageClick\",\n                s_oAPT: {\n                    actionId: 648\n                },\n                s_oEPN: \"My current Home Internet package\"\n            });\n        }\n    }, [expanded]);\n    var collapseIcon = expanded ? \"icon-Collapse\" : \"icon-Expand\";\n    return external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"section\", { className: \"bgVirginGradiant accss-focus-outline-override-pad\" },\n        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"container liquid-container sans-serif\" },\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"accordion-group internet-current-package flexCol\" },\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"accordion-heading col-xs-12 noPaddingImp\" },\n                    external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"a\", { id: \"accordion_expand_link\", href: \"javascript:void(0)\", onClick: function () { return toggleState(!expanded); }, \"aria-controls\": \"div1-accessible\", className: \"accordion-accessible-toggle txtSize18 txtDecorationNoneHover txtWhite flexRow align-items-center accss-width-fit-content\", \"aria-expanded\": expanded, role: \"button\" },\n                        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"span\", { className: \"sr-only accordion-label\", \"aria-live\": \"polite\", \"aria-atomic\": \"true\", \"aria-hidden\": \"true\" },\n                            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedMessage, { id: expanded ? \"Collapse\" : \"Expand\" })),\n                        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"span\", { className: \"\".concat(collapseIcon, \" virgin-icon txtSize24 virginRedIcon\"), \"aria-hidden\": \"true\" },\n                            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"span\", { className: \"virgin-icon path1 \".concat(collapseIcon) }),\n                            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"span\", { className: \"virgin-icon path2 \".concat(collapseIcon) })),\n                        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"margin-15-left flexCol\" },\n                            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"span\", { className: \"txtWhite txtBold txtSize18\" },\n                                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedMessage, { id: \"My current Home Internet package\" })),\n                            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"span\", { className: \"expand txtWhite txtSize12 no-margin-top\", style: { display: expanded ? \"none\" : undefined } },\n                                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedMessage, { id: \"Expand to view details\" }))))),\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { id: \"div1-accessible\", className: \"collapse-accordion-accessible-toggle accordion-body txtWhite col-xs-12 margin-5-top margin-40-left\", style: { display: expanded ? \"block\" : \"none\" } },\n                    external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"accordion-inner\" }, accountDetails.map(function (_a) {\n                        var Name = _a.Name, RegularPrice = _a.RegularPrice, PromotionDetails = _a.PromotionDetails;\n                        return external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"col-sm-5\" },\n                            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"spacer10\", \"aria-hidden\": \"true\" }),\n                            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"flexRow flexEnd\" },\n                                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"flexGrow\" }, Name),\n                                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { style: { whiteSpace: \"nowrap\" } },\n                                    external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Components.BellCurrency, { value: (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(RegularPrice, \"Price\", 0) }),\n                                    external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedMessage, { id: \"PER_MO\" }))),\n                            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Components.Visible, { when: !!PromotionDetails },\n                                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"spacer5\", \"aria-hidden\": \"true\" }),\n                                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Components.Visible, { when: (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(PromotionDetails, \"Description\", false) },\n                                    external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"flexRow\" },\n                                        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"flexGrow\" }, (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(PromotionDetails, \"Description\", \"\")),\n                                        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", null,\n                                            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Components.BellCurrency, { value: (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(PromotionDetails, \"PromotionalPrice.Price\", 0) }),\n                                            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedMessage, { id: \"PER_MO\" })))),\n                                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Components.Visible, { when: (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(PromotionDetails, \"ExpiryDate\", false) },\n                                    external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", null,\n                                        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedDate, { value: (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(PromotionDetails, \"ExpiryDate\", \"\"), format: \"yMMMMd\", timeZone: \"UTC\" }, function (expiryDate) { return external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedMessage, { id: \"PromotionExpires\", values: { expiryDate: expiryDate } }); })))));\n                    }))))));\n};\nvar Header = (0,external_root_ReactRedux_commonjs2_react_redux_commonjs_react_redux_amd_react_redux_.connect)(function (_a) {\n    var accountDetails = _a.accountDetails;\n    return ({ accountDetails: accountDetails || [] });\n})(Component);\n\n;// ../src/views/catalog/Package.tsx\n\n\n\n\n\n\nvar Visible = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Components.Visible, Currency = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Components.Currency;\nvar Package_Component = function (_a) {\n    var id = _a.id, name = _a.name, shortDescription = _a.shortDescription, usagePlan = _a.usagePlan, isSelectable = _a.isSelectable, isSelected = _a.isSelected, regularPrice = _a.regularPrice, promotionDetails = _a.promotionDetails, offeringAction = _a.offeringAction, onPackageClicked = _a.onPackageClicked;\n    var onPackageAction = function (e) {\n        e.stopPropagation();\n        e.preventDefault();\n        if (isSelected)\n            return;\n        if (e.keyCode === undefined || e.keyCode === 32 || e.keyCode === 13) {\n            onPackageClicked(offeringAction);\n        }\n    };\n    var _b = __read(external_root_React_commonjs2_react_commonjs_react_amd_react_.useState(false), 2), uploadExpanded = _b[0], ExpandUpload = _b[1];\n    var onUploadClick = function (e) {\n        if ((e.keyCode === undefined || e.keyCode === 13) && e.target.classList.contains(\"txtUnderline\")) {\n            ExpandUpload(!uploadExpanded);\n        }\n    };\n    external_root_React_commonjs2_react_commonjs_react_amd_react_.useEffect(function () {\n        $(\"#\" + id)\n            .find(\"[data-toggle]\")\n            .addClass(\"txtUnderline txtBlue pointer accss-text-blue-on-bg-white accss-width-fit-content\")\n            .attr(\"tabindex\", \"0\")\n            .next()\n            .addClass(\"downloadTray\")\n            .removeAttr(\"id\");\n    });\n    return external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { id: id, className: \"virgin-internet-box txtGray margin-15-bottom \".concat(isSelected ? \"selected\" : \"\") },\n        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"flexRow bgWhite border-radius-3 virgin-title-block pad-30 pad-15-left-right-sm accss-focus-outline-override-white-bg\" },\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"package_ctrl\" },\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"span\", { id: \"CTA_\".concat(id), className: \"graphical_ctrl ctrl_radioBtn pointer\", onClick: onPackageAction },\n                    external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"input\", { id: \"OPT_\".concat(id), name: \"internetpackage\", checked: isSelected, type: \"radio\", \"aria-labelledby\": \"PACKAGE_CTA_\".concat(id), \"aria-describedby\": \"PACKAGE_CTA_DESC_\".concat(id), \"aria-checked\": isSelected, className: \"radioBtn-active data-feature\" }),\n                    external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"span\", { className: \"ctrl_element pointer data-addon-active data-addon-border\" }))),\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"package-desc fill\" },\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { id: \"PACKAGE_CTA_\".concat(id), className: \"fill pad-15-left content-width valign-top pad-0-xs pointer\", onClick: onPackageAction },\n                    external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"h2\", { className: \"virginUltraReg txtSize16 floatL txtUppercase no-margin\" }, name)),\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"spacer10 d-none d-sm-block d-md-none clear\", \"aria-hidden\": \"true\" }),\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"spacer15 clear\", \"aria-hidden\": \"true\" }),\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"spacer1 bgGrayLight6 clear margin-30-right\", \"aria-hidden\": \"true\" }),\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"spacer15 hidden-m\", \"aria-hidden\": \"true\" }),\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"pkg-pull-left neg-margin-left-40-sm flexBlock\", id: \"PACKAGE_CTA_DESC_\".concat(id) },\n                    external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"flexRow fill flexCol-xs\" },\n                        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"ul\", { id: \"UPLOAD_CTA_\".concat(id), className: \"speed-box1 flexRow flexCol-xs mb-0 pl-0 list-unstyled \".concat(uploadExpanded ? \"expanded\" : \"\"), onKeyUp: onUploadClick, onClick: onUploadClick, dangerouslySetInnerHTML: { __html: shortDescription } }),\n                        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"ul\", { className: \"speed-box2 mb-0 list-unstyled\", dangerouslySetInnerHTML: { __html: usagePlan } }),\n                        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"speed-box3\" },\n                            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"pad-30-left no-pad-xs margin-10-left-xs\" },\n                                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(Visible, { when: (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(promotionDetails, \"expiryDate\", false) },\n                                    external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"span\", { className: \"txtSize12 txtBlack block txtBold bgGray2 pad-5 border-radius-3\" },\n                                        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedDate, { value: (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(promotionDetails, \"expiryDate\", \"\"), format: \"yMMMMd\", timeZone: \"UTC\" }, function (expiryDate) { return external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedMessage, { id: \"Your monthly credit expires\", values: { expiryDate: expiryDate } }); }))),\n                                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(Visible, { when: (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(promotionDetails, \"discountDuration\", false) },\n                                    external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"span\", { className: \"txtSize12 txtBlack block txtBold bgGray2 pad-5 border-radius-3\" },\n                                        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedMessage, { id: \"Get a credit for months\", values: { credit: Math.abs((0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(promotionDetails, \"discountPrice.price\", 0)), duration: (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(promotionDetails, \"discountDuration\", 0) } }))),\n                                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(Visible, { when: (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(!promotionDetails, undefined, false) },\n                                    external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"spacer15 clear hidden-m\", \"aria-hidden\": \"true\" })),\n                                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"price virginUltraReg txtSize40 line-height-1 margin-10-top\" },\n                                    external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(Visible, { when: (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(promotionDetails, undefined, false) },\n                                        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedMessage, { id: \"Now\" }),\n                                        \"\\u00A0\"),\n                                    external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(Currency, { value: (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(promotionDetails, \"?promotionalPrice.price\", false) === false\n                                            ? (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(regularPrice, \"price\", 0)\n                                            : (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(promotionDetails, \"promotionalPrice.price\", 0), monthly: true }),\n                                    external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(Visible, { when: (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(promotionDetails, undefined, false) },\n                                        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"p\", { className: \"txtSize12 txtBlack txtBold sans-serif no-margin\" },\n                                            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedMessage, { id: \"Current Price\", values: (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(regularPrice, undefined, {}) })))),\n                                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(Visible, { when: (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(promotionDetails, undefined, false) },\n                                    external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"p\", { className: \"txtSize12 txtBlack sans-serif no-margin pad-10-top\" }, (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(promotionDetails, \"legalMessage\", external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedMessage, { id: \"Prices may increase legal\" })))))))))));\n};\nvar Package = (0,external_root_ReactRedux_commonjs2_react_redux_commonjs_react_redux_amd_react_redux_.connect)(function (_a) { return ({}); }, function (dispatch) { return ({\n    onPackageClicked: function (action) { return dispatch(togglePackageSelection(action)); }\n}); })(Package_Component);\n\n;// ../src/views/catalog/Legal.tsx\n\n\n\n\nvar Footer = function () {\n    var _a = __read(external_root_React_commonjs2_react_commonjs_react_amd_react_.useState(false), 2), expanded = _a[0], toggleState = _a[1];\n    external_root_React_commonjs2_react_commonjs_react_amd_react_.useEffect(function () {\n        expanded &&\n            external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Omniture.useOmniture().trackAction({\n                id: \"ligalStuffClick\",\n                s_oAPT: {\n                    actionId: 648\n                },\n                s_oEPN: \"Legal Stuff\"\n            });\n    }, [expanded]);\n    return external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"virginUltraReg margin-15-top\", id: \"moreInfo\" },\n        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"button\", { id: \"Legal_stuff\", className: \"btn btn-link noUnderlineAll noPaddingImp links-blue-on-bg-gray accss-focus-outline-override-grey-bg-element\", onClick: function () { return toggleState(!expanded); }, \"aria-expanded\": expanded },\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"span\", { className: \"volt-icon \".concat(expanded ? \"icon-collapse_m\" : \"icon-expand_m\"), \"aria-hidden\": \"true\" }),\n            \"\\u00A0\\u00A0\",\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedMessage, { id: \"Legal stuff label\" })),\n        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"spacer30\", \"aria-hidden\": \"true\" }),\n        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Components.Visible, { when: expanded },\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"moreInfoBox bgWhite pad30 margin-30-bottom accss-link-override accss-focus-outline-override-white-bg\" },\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"button\", { id: \"LEGALBOX_CLOSE\", type: \"button\", onClick: function () { return toggleState(false); }, className: \"close moreInfoLink x-inner txtDarkGrey txtSize18 txtBold\", \"aria-label\": \"close\" },\n                    external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"span\", { className: \"virgin-icon icon-big_X\", \"aria-hidden\": \"true\" })),\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.FormattedHTMLMessage, { id: \"GOOD TO KNOW\" }))));\n};\n\n;// ../src/utils/Characteristics.ts\n\nfunction toCharacteristicsJSON(charactgerstics) {\n    return (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(charactgerstics, undefined, []).reduce(function (json, charactgerstic) {\n        if (charactgerstic.name) {\n            json[charactgerstic.name] = charactgerstic.value;\n        }\n        return json;\n    }, {});\n}\n\n;// ../src/views/catalog/index.tsx\n\n\n\n\n\n\n\nvar catalog_Component = function (_a) {\n    var catalog = _a.catalog;\n    return external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"container liquid-container noSpacing\", role: \"radiogroup\" },\n        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"style\", null, \".icon-upload-ico:before {\\n                    content: \\\"\\\\e99d\\\";\\n                }\\n                .icon-download-ico:before {\\n                    content: \\\"\\\\e929\\\";\\n                }\\n                .package-desc li {\\n                    display: block;\\n                    list-style: none;\\n                    position: relative;\\n                    width: calc(100% / 2);\\n                }\\n                    .package-desc li:not(:last-of-type) {\\n                        padding-right: 15px;\\n                    }\\n                    .package-desc li .volt-icon {\\n                        position: absolute;\\n                        display: block;\\n                        color: #cc0000;\\n                        font-size: 32px;\\n                        width: 42px;\\n                        height: 42px;\\n                        left: 0;\\n                    }\\n                    .package-desc li span {\\n                        display: block;\\n                        font-size: 12px;\\n                    }\\n                    .package-desc .speed-box2 span:first-of-type,\\n                    .package-desc li span.speed {\\n                        font-size: 22px;\\n                        color: black;\\n                        text-transform: uppercase;\\n                        font-family: \\\"VMUltramagneticNormalRegular\\\", Helvetica, Arial, sans-serif;\\n                }\\n                .package-desc .speed-box2 span.usage {\\n                    white-space: nowrap;\\n                }\\n                .speed-box1 li {\\n                    margin-top: 10px;\\n                    padding-left: 42px;\\n                }\\n                .package-desc li span.downloadTray {\\n                    display: none;\\n                }\\n                .package-desc .speed-box1.expanded li span.downloadTray {\\n                    display: block;\\n                }\\n                @media (max-width: 991.98px) {\\n                    .package-desc li {\\n                        width: 100%;\\n                    }\\n                  .pkg-pull-left {\\n                      margin-left: -40px;\\n                  }\\n                }\"),\n        catalog.filter(function (pkg) { return !pkg.isCurrent; })\n            .sort(function (a, b) { return ((0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(toCharacteristicsJSON(a.characteristics), \"sortPriority\", 0) -\n            (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(toCharacteristicsJSON(b.characteristics), \"sortPriority\", 0)); })\n            .map(function (internetPackage) { return external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(Package, __assign({}, internetPackage)); }),\n        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(Footer, null));\n};\nvar Catalog = (0,external_root_ReactRedux_commonjs2_react_redux_commonjs_react_redux_amd_react_redux_.connect)(function (_a) {\n    var catalog = _a.catalog;\n    return ({ catalog: catalog });\n})(catalog_Component);\n\n;// ../src/views/index.tsx\n\n\n\n\n\n\nvar RestrictionModal = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Components.RestrictionModal;\nvar views_errorOccured = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.errorOccured, widgetRenderComplete = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.widgetRenderComplete;\nvar views_Component = (function (_super) {\n    __extends(Component, _super);\n    function Component() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    Component.prototype.componentDidCatch = function (err) {\n        this.props.onErrorEncountered(err);\n    };\n    Component.prototype.componentDidMount = function () {\n        this.props.widgetRenderComplete(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetName.INTERNET);\n    };\n    Component.prototype.render = function () {\n        return external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"main\", { id: \"mainContent\" },\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(Header, null),\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"spacer30\", \"aria-hidden\": \"true\" }),\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(Catalog, null),\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(RestrictionModal, { id: \"INTERNET_RESTRICTION_MODAL\" }));\n    };\n    return Component;\n}(external_root_React_commonjs2_react_commonjs_react_amd_react_.Component));\nvar Application = (0,external_root_ReactRedux_commonjs2_react_redux_commonjs_react_redux_amd_react_redux_.connect)(function (_a) { return ({}); }, function (dispatch) { return ({\n    onErrorEncountered: function (error) { return dispatch(views_errorOccured(error)); },\n    widgetRenderComplete: function () { return dispatch(widgetRenderComplete()); }\n}); })(views_Component);\n\n;// ../src/App.tsx\n\n\n\nvar ApplicationRoot = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Components.ApplicationRoot;\nvar App = function () { return external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(ApplicationRoot, null,\n    external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(Application, null)); };\n\n;// ../src/Widget.tsx\n\n\n\n\n\n\n\n\n\nvar setWidgetProps = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.setWidgetProps, Widget_setWidgetStatus = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.setWidgetStatus;\nvar WidgetContainer = (function (_super) {\n    __extends(WidgetContainer, _super);\n    function WidgetContainer(store, params, config, pipe) {\n        var _this = _super.call(this) || this;\n        _this.store = store;\n        _this.params = params;\n        _this.config = config;\n        _this.pipe = pipe;\n        return _this;\n    }\n    WidgetContainer.prototype.init = function () {\n        this.pipe.subscribe(Pipe.Subscriptions(this.store));\n        this.store.dispatch(setWidgetProps(this.config));\n        this.store.dispatch(setWidgetProps(this.params.props));\n        this.store.dispatch(Widget_setWidgetStatus(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetStatus.INIT));\n    };\n    WidgetContainer.prototype.destroy = function () {\n        this.pipe.unsubscribe();\n        this.store.destroy();\n    };\n    WidgetContainer.prototype.render = function (root) {\n        var store = this.store;\n        root.render(external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ContextProvider, { value: { config: this.config } },\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactRedux_commonjs2_react_redux_commonjs_react_redux_amd_react_redux_.Provider, { store: store },\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(App, null))));\n    };\n    WidgetContainer = __decorate([\n        (0,external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.Widget)({ namespace: \"Ordering\" }),\n        __metadata(\"design:paramtypes\", [Store, external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.ParamsProvider, Config, Pipe])\n    ], WidgetContainer);\n    return WidgetContainer;\n}(external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.ViewWidget));\n/* harmony default export */ var Widget = (WidgetContainer);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../src/Widget.tsx\n\n}");

/***/ }),

/***/ "bwtk":
/*!**********************************************************************************!*\
  !*** external {"root":"bwtk","commonjs2":"bwtk","commonjs":"bwtk","amd":"bwtk"} ***!
  \**********************************************************************************/
/***/ (function(module) {

module.exports = __WEBPACK_EXTERNAL_MODULE_bwtk__;

/***/ }),

/***/ "omf-changepackage-components":
/*!********************************************************************************************************************************************************************************!*\
  !*** external {"root":"OMFChangepackageComponents","commonjs2":"omf-changepackage-components","commonjs":"omf-changepackage-components","amd":"omf-changepackage-components"} ***!
  \********************************************************************************************************************************************************************************/
/***/ (function(module) {

module.exports = __WEBPACK_EXTERNAL_MODULE_omf_changepackage_components__;

/***/ }),

/***/ "react":
/*!**************************************************************************************!*\
  !*** external {"root":"React","commonjs2":"react","commonjs":"react","amd":"react"} ***!
  \**************************************************************************************/
/***/ (function(module) {

module.exports = __WEBPACK_EXTERNAL_MODULE_react__;

/***/ }),

/***/ "react-intl":
/*!*********************************************************************************************************!*\
  !*** external {"root":"ReactIntl","commonjs2":"react-intl","commonjs":"react-intl","amd":"react-intl"} ***!
  \*********************************************************************************************************/
/***/ (function(module) {

module.exports = __WEBPACK_EXTERNAL_MODULE_react_intl__;

/***/ }),

/***/ "react-redux":
/*!*************************************************************************************************************!*\
  !*** external {"root":"ReactRedux","commonjs2":"react-redux","commonjs":"react-redux","amd":"react-redux"} ***!
  \*************************************************************************************************************/
/***/ (function(module) {

module.exports = __WEBPACK_EXTERNAL_MODULE_react_redux__;

/***/ }),

/***/ "redux":
/*!**************************************************************************************!*\
  !*** external {"root":"Redux","commonjs2":"redux","commonjs":"redux","amd":"redux"} ***!
  \**************************************************************************************/
/***/ (function(module) {

module.exports = __WEBPACK_EXTERNAL_MODULE_redux__;

/***/ }),

/***/ "redux-actions":
/*!*********************************************************************************************************************!*\
  !*** external {"root":"ReduxActions","commonjs2":"redux-actions","commonjs":"redux-actions","amd":"redux-actions"} ***!
  \*********************************************************************************************************************/
/***/ (function(module) {

module.exports = __WEBPACK_EXTERNAL_MODULE_redux_actions__;

/***/ }),

/***/ "redux-observable":
/*!*********************************************************************************************************************************!*\
  !*** external {"root":"ReduxObservable","commonjs2":"redux-observable","commonjs":"redux-observable","amd":"redux-observable"} ***!
  \*********************************************************************************************************************************/
/***/ (function(module) {

module.exports = __WEBPACK_EXTERNAL_MODULE_redux_observable__;

/***/ }),

/***/ "rxjs":
/*!**********************************************************************************!*\
  !*** external {"root":"rxjs","commonjs2":"rxjs","commonjs":"rxjs","amd":"rxjs"} ***!
  \**********************************************************************************/
/***/ (function(module) {

module.exports = __WEBPACK_EXTERNAL_MODULE_rxjs__;

/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			// no module.id needed
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId](module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/************************************************************************/
/******/ 	/* webpack/runtime/define property getters */
/******/ 	!function() {
/******/ 		// define getter functions for harmony exports
/******/ 		__webpack_require__.d = function(exports, definition) {
/******/ 			for(var key in definition) {
/******/ 				if(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
/******/ 					Object.defineProperty(exports, key, { enumerable: true, get: definition[key] });
/******/ 				}
/******/ 			}
/******/ 		};
/******/ 	}();
/******/ 	
/******/ 	/* webpack/runtime/hasOwnProperty shorthand */
/******/ 	!function() {
/******/ 		__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }
/******/ 	}();
/******/ 	
/******/ 	/* webpack/runtime/make namespace object */
/******/ 	!function() {
/******/ 		// define __esModule on exports
/******/ 		__webpack_require__.r = function(exports) {
/******/ 			if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 				Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 			}
/******/ 			Object.defineProperty(exports, '__esModule', { value: true });
/******/ 		};
/******/ 	}();
/******/ 	
/************************************************************************/
/******/ 	
/******/ 	// startup
/******/ 	// Load entry module and return exports
/******/ 	// This entry module can't be inlined because the eval-source-map devtool is used.
/******/ 	var __webpack_exports__ = __webpack_require__("../src/Widget.tsx");
/******/ 	
/******/ 	return __webpack_exports__;
/******/ })()
;
});