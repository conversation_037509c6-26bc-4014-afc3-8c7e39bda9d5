import { EWidgetRoute, ValueOf, Volt } from "omf-changepackage-components";
import {
  IAccountDetail,
  INavigationItem,
  IProductOffering,
  IServiceAccountAPI,
  ITVCatalog,
  ITVChannel
} from "../models";
import { initSearch } from "../utils/Search";

export function serviceAccountMutatorFn(
  response: IServiceAccountAPI
): Array<IAccountDetail> {
  const offerings = ValueOf<Array<IProductOffering>>(
    response,
    "ProductOfferings",
    []
  );
  const accountDetails: Array<IAccountDetail> = [],
    keys: Array<string> = [];
  const getSortPriority = (offering: any): number => {
    const priority = ValueOf(offering, "Characteristics", []).find(
      (item: any) => item.Name === "sortPriority"
    );
    return ValueOf(priority, "Value", 0) * 1;
  };
  offerings.forEach(offering => {
    if (
      offering.DisplayGroupKey &&
      keys.indexOf(offering.DisplayGroupKey) < 0
    ) {
      keys.push(offering.DisplayGroupKey);
      accountDetails.push({
        displayGroupKey: offering.DisplayGroupKey,
        offerings: offerings
          .filter(item => item.DisplayGroupKey === offering.DisplayGroupKey)
          .sort((a, b) => getSortPriority(a) - getSortPriority(b))
      });
    }
  });
  return accountDetails.sort((a: any, b: any) => {
    switch (true) {
      case a.displayGroupKey === "BASE_PROGRAMMING": return -1;
      case a.displayGroupKey === "PROMOTION": return 1;
      default: return 0;
    }
  });
}

// function channelReducer(channels: Array<ITVChannel>, offering: Volt.IProductOffering): Array<ITVChannel> {
//     if (
//         ValueOf(offering, "productOfferingType", false) === Volt.EProductOfferingType.CHANNEL
//     ) {
//         channels.push(offering as ITVChannel);
//     } else {
//         const childOfferings = ValueOf(offering, "childOfferings", []);
//         channels = channels.concat(childOfferings.reduce(channelReducer, []));
//     }
//     return channels;
// }

function collectChannlesFromAllOfferings(productOfferings: Array<Volt.IProductOffering>): Array<ITVChannel> {
  const duplicateFilter: Array<string> = [], collection: Array<ITVChannel> = [];
  function appendChannelToCollection(channel: any) {
    if (channel.productOfferingType === Volt.EProductOfferingType.CHANNEL &&
      duplicateFilter.indexOf(channel.id) < 0) {
      collection.push(channel);
      duplicateFilter.push(channel.id);
    }
  }
  productOfferings.forEach(offering => {
    if (Array.isArray(offering.childOfferings)) {
      offering.childOfferings.forEach(appendChannelToCollection);
    } else {
      appendChannelToCollection(offering);
    }
  });
  return collection;
}

export function catalogMutatorFn(response: Volt.IAPIResponse): ITVCatalog {
  const productOfferingGroup: Volt.IProductOfferingGroup = ValueOf(
    response,
    "productOfferingDetail.productOfferingGroups",
    []
  ).find((group: Volt.IProductOfferingGroup) => group.lineOfBusiness === "TV");
  const productOfferingList: Array<Volt.IProductOffering> = ValueOf(
    productOfferingGroup,
    "productOfferings",
    []
  );
  const offerings = productOfferingList
    .filter(
      offering =>
        offering.displayGroupKey &&
        offering.displayGroupKey !== Volt.EDIsplayGroupKey.NONE
    )
    .reduce((collection: any, offering) => {
      const key = offering.displayGroupKey;
      collection[key] = collection[key] || [];
      collection[key].push(offering);
      return collection;
    }, {} as Array<{ [key: string]: Array<Volt.IProductOffering> }>);
  const catalog: ITVCatalog = {
    index: productOfferingList,
    offerings,
    channels: collectChannlesFromAllOfferings(productOfferingList)
    // productOfferingList.filter(
    //   offering =>
    //     offering.productOfferingType === Volt.EProductOfferingType.CHANNEL
    // ) as Array<ITVChannel> // .reduce(channelReducer, [])
  };

  (catalog as any)["refresh"] = Math.random() * 1000;

  // Init search indexing
  initSearch(catalog.channels);
  return catalog;
}

export function orderMutatorFn(
  response: Volt.IAPIResponse,
  catalog: ITVCatalog
): ITVCatalog {
  const applyDiff = (list: Array<Volt.IProductOffering>) => (offering: Volt.IProductOffering) => {
    const src = list.find(item => item.id === offering.id);
    if (src) {
      src.isCurrent = offering.isCurrent;
      src.isDisabled = offering.isDisabled;
      src.isSelectable = offering.isSelectable;
      src.isSelected = offering.isSelected;
      src.isAlreadyIncludedIn = offering.isAlreadyIncludedIn;
      // src.multipleWaysToAdd = offering.multipleWaysToAdd;
      src.offeringAction = offering.offeringAction;
      src.promotionDetails = offering.promotionDetails;
      if (src.childOfferings && offering.childOfferings) offering.childOfferings.forEach(applyDiff(src.childOfferings));
    } else {
      // Do something if new product

    }
  };
  const productOfferingGroup: Volt.IProductOfferingGroup = ValueOf(
    response,
    "productOfferingDetail.productOfferingGroups",
    []
  ).find((group: Volt.IProductOfferingGroup) => group.lineOfBusiness === "TV");
  const productOfferingList: Array<Volt.IProductOffering> = ValueOf(
    productOfferingGroup,
    "productOfferings",
    []
  );
  if (ValueOf(productOfferingGroup, "productOfferingGroupType", "") === "Delta") {
    productOfferingList.forEach(applyDiff(catalog.index));
    (catalog.offerings as any)["refresh"] = Math.random() * 1000;
    catalog.channels = [...catalog.channels];
    // udpate search indexing
    initSearch(catalog.channels);
  } else {
    catalog = catalogMutatorFn(response);
  }

  return { ...catalog, refresh: Math.random() * 1000 } as ITVCatalog;
}

/*
    TV_BASE_PRODUCT,
    ALACARTE,
    MOVIE,
    TV,
    SPECIALITY_SPORTS,
    ADD_ON,
    INTERNATIONAL,
    BASE_PROGRAMMING,
    SPECIALITY_CHANNELS,
    OFFERS,
    NONE
*/

export function navigationMutatorFn(
  response: Volt.IAPIResponse
): Array<INavigationItem> {
  const itemSorter = (a: INavigationItem, b: INavigationItem): number =>
    a.sortPriority - b.sortPriority;
  const displayGroups = ValueOf(
    response,
    "productOfferingDetail.displayGroup",
    {}
  );
  const baseOffering = ValueOf<INavigationItem>(
    displayGroups,
    "baseOffering",
    null
  );
  const additionalOfferings = ValueOf(displayGroups, "additionalOfferings", [])
    .filter(
      (offering: INavigationItem) =>
        offering.key && offering.key !== Volt.EDIsplayGroupKey.NONE
    )
    .map((offering: INavigationItem) => ({
      ...offering,
      offeringKey: offering.key
    }));
  const rootOffering = additionalOfferings.filter(
    (offering: INavigationItem) => offering.isRoot
  );
  const suplimentaryOffering = additionalOfferings.filter(
    (offering: INavigationItem) => !offering.isRoot
  );
  // Remove count prop from base package
  // so it does not show on the sidebar
  delete baseOffering.count;
  const navigation: Array<INavigationItem> = baseOffering
    ? [baseOffering, ...rootOffering]
    : rootOffering;
  navigation.push({
    offeringKey: Volt.EDIsplayGroupKey.TV_BROWSE_ALL,
    sortPriority: 99
  } as INavigationItem);
  navigation.forEach((offering: INavigationItem) => {
    offering.offeringKey = offering.offeringKey || offering.key;
    offering.children = suplimentaryOffering
      .filter((child: INavigationItem) => child.parentKey === offering.key)
      .sort(itemSorter)
      .map((child: INavigationItem) => {
        // child.route = `#${child.key}`;
        switch (child.offeringKey) {
          case Volt.EDIsplayGroupKey.INTERNATIONAL_COMBOS:
            child.route = EWidgetRoute.TV_InternationalCombos;
            break;
          case Volt.EDIsplayGroupKey.INTERNATIONAL_ALACARTE:
            child.route = EWidgetRoute.TV_InternationalAlacarte;
            break;
        }
        return child;
      });
    switch (offering.offeringKey) {
      case Volt.EDIsplayGroupKey.BASE_PROGRAMMING:
      case Volt.EDIsplayGroupKey.TV_BASE_PRODUCT:
        offering.route = EWidgetRoute.TV_Packages;
        break;
      case Volt.EDIsplayGroupKey.ALACARTE:
        offering.route = EWidgetRoute.TV_Alacarte;
        break;
      case Volt.EDIsplayGroupKey.MOVIE:
        offering.route = EWidgetRoute.TV_MoviesSeries;
        break;
      case Volt.EDIsplayGroupKey.ADD_ON:
        offering.route = EWidgetRoute.TV_Addons;
        break;
      case Volt.EDIsplayGroupKey.INTERNATIONAL:
        offering.route = EWidgetRoute.TV_International;
        break;
      case Volt.EDIsplayGroupKey.TV_BROWSE_ALL:
        offering.route = EWidgetRoute.TV_Browse;
        break;
    }
    (offering as any)["refresh"] = Math.random() * 1000;
  });
  navigation.filter(
    (offering: INavigationItem) => !Boolean(offering.parentDisplayGroup)
  );
  return navigation.sort(itemSorter);
}
