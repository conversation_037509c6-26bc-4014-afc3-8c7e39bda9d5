import { combineReducers } from "redux";
import { Action, handleActions } from "redux-actions";
import { combineEpics } from "redux-observable";
import { Reducers, LifecycleEpics, RestricitonsEpics, ModalEpics, Actions } from "omf-changepackage-components";

import { Store as BwtkStore, Injectable, CommonFeatures } from "bwtk";

import * as actions from "./Actions";

import { IStoreState, INavigationItem, ITVCatalog, IAccountDetail } from "../models";
import { Epics } from "./Epics";
import { Localization } from "../Localization";
import { Client } from "../Client";

const { BaseStore, actionsToComputedPropertyName } = CommonFeatures;
const {
  setNavigation,
  setCatalog,
  updateCatalog,
  setAccountDetails
} = actionsToComputedPropertyName(actions);

const {
  handleNav
} = actionsToComputedPropertyName(Actions);

@Injectable
export class Store extends BaseStore<IStoreState> {
  constructor(private client: Client, store: BwtkStore, private epics: Epics, private localization: Localization) {
    super(store);
  }

  get reducer() {
    return combineReducers<IStoreState>({
      // =========== Widget lifecycle methods =============
      ...Reducers.WidgetBaseLifecycle(this.localization) as any,
      ...Reducers.WidgetLightboxes(),
      ...Reducers.WidgetRestrictions(),
      // =========== Widget data ===============
      navigation: handleActions<Array<INavigationItem> | null>({
        [setNavigation]: (state, { payload }: Action<Array<INavigationItem>>) => payload || state,
      }, []),
      accountDetails: handleActions<Array<IAccountDetail>>({
        [setAccountDetails]: (state, { payload }: Action<Array<IAccountDetail>>) => payload || state,
      }, []),
      catalog: handleActions<ITVCatalog | null>({
        [setCatalog]: (state, { payload }: Action<ITVCatalog>) => payload || state,
        [updateCatalog]: (state, { payload }: Action<ITVCatalog>) => payload || state,
      }, {} as ITVCatalog),
      navStatus: handleActions<boolean>({
        [handleNav]: (state, { payload }: Action<boolean>) => payload
      }, false)
    });
  }

  /**
   * Middlewares are collected bottom-to-top
   * so, the bottom-most epic will receive the
   * action first, while the top-most -- last
   * @readonly
   * @memberof Store
   */
  get middlewares(): any {
    return combineEpics(this.epics.omnitureEpics.combineEpics(), this.epics.userAccountEpic.combineEpics(), 
      this.epics.catalogEpics.combineEpics(), this.epics.orderingEpics.combineEpics(),
      this.epics.combineEpics(), new ModalEpics().combineEpics(), 
      new RestricitonsEpics(this.client, "TV_RESTRICTION_MODAL").combineEpics(), new LifecycleEpics().combineEpics()
    );
  }
}
